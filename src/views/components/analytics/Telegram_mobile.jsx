import React, { useState } from "react";

function Telegram_mobile() {
  const [selectedCountry, setSelectedCountry] = useState("India");
  const [phoneNumber, setPhoneNumber] = useState("");

  return (
    <div className="min-h-screen bg-white flex flex-col items-center px-6 py-8">
      {/* Telegram Logo */}
      <div className="mb-8 mt-16">
        <div className="w-24 h-24 bg-blue-400 rounded-full flex items-center justify-center">
          <svg
            className="w-12 h-12 text-white"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4.64 6.8c-.15 1.58-.8 5.42-1.13 7.19-.14.75-.42 1-.68 1.03-.58.05-1.02-.38-1.58-.75-.88-.58-1.38-.94-2.23-1.5-.99-.65-.35-1.01.22-1.59.15-.15 2.71-2.48 2.76-2.69a.2.2 0 00-.05-.18c-.06-.05-.14-.03-.21-.02-.09.02-.38.24-1.07.7-.96.66-1.89 1.31-1.89 1.31s-.27.17-.78.02c-.5-.16-1.07-.36-1.07-.36s-.8-.5.56-1.02c3.61-1.6 6.05-2.66 6.84-3.05.75-.37 1.99-.87 1.99.87z" />
          </svg>
        </div>
      </div>

      {/* Title */}
      <h1 className="text-2xl font-medium text-gray-900 mb-4">Telegram</h1>

      {/* Subtitle */}
      <p className="text-gray-500 text-center mb-12 px-4 leading-relaxed">
        Please confirm your country code and enter your phone number.
      </p>

      {/* Country Selection */}
      <div className="w-full max-w-sm mb-6">
        <label className="block text-gray-600 text-sm mb-2">Country</label>
        <div className="relative">
          <select
            className="w-full px-4 py-3 border border-gray-200 rounded-lg appearance-none bg-white focus:outline-none focus:border-blue-400 pr-10"
            value={selectedCountry}
            onChange={(e) => setSelectedCountry(e.target.value)}
          >
            <option value="India">🇮🇳 India</option>
            <option value="USA">🇺🇸 United States</option>
            <option value="UK">🇬🇧 United Kingdom</option>
            <option value="Canada">🇨🇦 Canada</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
            <svg
              className="w-4 h-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Mobile Number Input */}
      <div className="w-full max-w-sm mb-12">
        <label className="block text-gray-600 text-sm mb-2">
          Mobile Number
        </label>
        <div className="flex">
          <div className="flex items-center px-3 py-3 border border-gray-200 border-r-0 rounded-l-lg bg-gray-50">
            <span className="text-sm">🇮🇳 +91</span>
          </div>
          <input
            type="tel"
            placeholder="Enter Mobile Number"
            className="flex-1 px-4 py-3 border border-gray-200 rounded-r-lg focus:outline-none focus:border-blue-400"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
          />
        </div>
      </div>

      {/* Send OTP Button */}
      <button className="w-full max-w-sm bg-blue-400 text-white py-4 rounded-lg font-medium text-lg hover:bg-blue-500 transition-colors">
        Send OTP
      </button>
    </div>
  );
}

export default Telegram_mobile;
